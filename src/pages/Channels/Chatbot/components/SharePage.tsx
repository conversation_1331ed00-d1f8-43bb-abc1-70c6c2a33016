import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {Co<PERSON>, Check, ExternalLink, Share2, Globe, Link2} from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";
import { useProject } from "@/context/ProjectContext";
import { useAuth } from "@/context/AuthContext";
import { generateEmbedCode } from "@/utils/embedCode";
import { useNavigate } from "react-router-dom";

const SharePage = () => {
  const { t } = useTranslation();
  const { currentProject } = useProject();
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  const [isCopied, setIsCopied] = useState(false);
  const [shareUrl, setShareUrl] = useState("");

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, isLoading, navigate]);

  useEffect(() => {
    if (currentProject) {
      // 生成分享链接
      const baseUrl = window.location.origin;
      const url = `${baseUrl}/share/${currentProject.id}`;
      setShareUrl(url);
    }
  }, [currentProject]);

  const embedCode = currentProject ? generateEmbedCode({
    projectId: currentProject.id,
  }) : "";

  const copyShareUrl = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setIsCopied(true);
      setTimeout(() => {
        setIsCopied(false);
      }, 2000);
    } catch (error) {
      console.error("Copy Failed:", error);
      // 降级方案
      try {
        const textArea = document.createElement("textarea");
        textArea.value = shareUrl;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
        setIsCopied(true);
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
      } catch (fallbackError) {
        console.error("Copy Failed:", fallbackError);
      }
    }
  };

  if (isLoading) {
    return (
        <div className="flex items-center justify-center h-screen overflow-hidden">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">{t("loading")}</p>
          </div>
        </div>
    );
  }

  if (!currentProject) {
    return (
        <div className="flex items-center justify-center h-screen overflow-hidden">
          <div className="text-center">
            <Globe className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">{t("noProjectSelected")}</p>
          </div>
        </div>
    );
  }

  return (
      <div className="h-screen flex flex-col overflow-hidden">
        {/* 头部固定高度区域 */}
        <div className="bg-white border-b border-gray-200 px-6 py-4 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <Link2 className="mr-2 h-6 w-6" />
                  Your Chat Page is Live
                </h1>
                <p className="text-sm text-gray-600 mt-1">
                  Send the link to teammates or customers and Ask your bot real questions
                </p>
              </div>
            </div>

            {/* 分享链接区域 */}
            <div className="flex items-center gap-2">
              <input
                  type="text"
                  value={shareUrl}
                  readOnly
                  className="w-80 px-3 py-2 text-xs border border-gray-300 rounded-md bg-gray-50 font-mono"
              />
              <Button
                  onClick={copyShareUrl}
                  size="sm"
                  variant={isCopied ? "default" : "outline"}
                  className={isCopied ? "bg-green-600 hover:bg-green-700" : ""}
              >
                {isCopied ? (
                    <>
                      <Check className="h-3 w-3 mr-1" />
                      Copied!
                    </>
                ) : (
                    <>
                      <Copy className="h-3 w-3 mr-1" />
                      Copy
                    </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* 下方：网站预览区域 - 占用剩余所有空间 */}
        <div className="flex-1 bg-gray-100 min-h-0 overflow-hidden">
          <iframe
              src={currentProject.website}
              className="w-full h-full border-0"
              title="Website Preview"
              sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
          />
        </div>
      </div>
  );
};

export default SharePage;