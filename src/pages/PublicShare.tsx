import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { Globe, AlertCircle } from "lucide-react";
import { getWidgetConfig } from "@/config";



interface ProjectSettings {
  logo: string;
  name: string;
  color: string;
  welcomeMsg: string;
  suggestedEnable: boolean;
  suggestedQuestions: string[];
}

interface Project {
  id: string;
  name: string;
  website: string;
  createTime: string;
  settings: ProjectSettings;
}

const PublicShare = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProject = async () => {
      if (!projectId) {
        setError("项目ID不存在");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        // 使用widget配置获取项目信息
        const widgetConfig = getWidgetConfig();
        const response = await fetch(`${widgetConfig.api.projectsEndpoint}/${projectId}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const projectData = await response.json();
        setProject(projectData);
      } catch (err) {
        console.error("加载项目失败:", err);
        setError("项目不存在或已被删除");
      } finally {
        setLoading(false);
      }
    };

    loadProject();
  }, [projectId]);

  // 动态加载聊天窗口脚本
  useEffect(() => {
    if (project) {
      // 清理之前的脚本
      const existingScript = document.querySelector('script[project-id]');
      if (existingScript) {
        existingScript.remove();
      }

      // 创建新的脚本标签并添加到head
      const script = document.createElement('script');
      script.async = true;
      script.src = 'https://www.yapyapbot.com/widget.js';
      script.setAttribute('project-id', project.id);
      document.head.appendChild(script);

      // 清理函数
      return () => {
        const scriptToRemove = document.querySelector('script[project-id]');
        if (scriptToRemove) {
          scriptToRemove.remove();
        }
      };
    }
  }, [project]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg">正在加载体验页面...</p>
        </div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md mx-auto p-6">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">页面不存在</h1>
          <p className="text-gray-600 mb-6">
            {error || "您访问的体验页面不存在或已被删除"}
          </p>
          <a
            href="/"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Globe className="h-4 w-4 mr-2" />
            返回首页
          </a>
        </div>
      </div>
    );
  }

  return (
      <div className="h-screen bg-gray-50 flex flex-col overflow-hidden">
        {/* 顶部信息栏 */}
        <div className="bg-white border-b border-gray-200 px-4 py-3 flex-shrink-0">
          <div className="max-w-7xl mx-auto flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {project.settings.logo && (
                  <img
                      src={project.settings.logo}
                      alt={project.settings.name}
                      className="h-8 w-8 rounded-full object-cover"
                  />
              )}
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  {project.settings.name || project.name}
                </h1>
                <p className="text-sm text-gray-600">Test your bot with real questions</p>
              </div>
            </div>
            <div className="text-sm text-gray-500">
              Website: {project.website && new URL(project.website).hostname}
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 relative">
          <iframe
              src={project.website}
              className="absolute inset-0 w-full h-full border-0"
              title="Website Preview"
              sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
          />
        </div>

        {/* 底部品牌信息 */}
        <div className="fixed bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg border border-gray-200">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <span>Powered by</span>
            <a
                href="/"
                className="font-semibold text-blue-600 hover:text-blue-700"
            >
              YapYapBot
            </a>
          </div>
        </div>
      </div>
  );
};

export default PublicShare;
